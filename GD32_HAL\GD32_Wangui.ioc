#MicroXplorer Configuration settings - do not modify
ADC1.Channel-3\#ChannelRegularConversion=ADC_CHANNEL_10
ADC1.Channel-5\#ChannelRegularConversion=ADC_CHANNEL_5
ADC1.ContinuousConvMode=DISABLE
ADC1.DMAContinuousRequests=DISABLE
ADC1.EnableAnalogWatchDog=false
ADC1.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T3_TRGO
ADC1.IPParameters=Rank-3\#ChannelRegularConversion,master,Channel-3\#ChannelRegularConversion,SamplingTime-3\#ChannelRegularConversion,NbrOfConversionFlag,DMAContinuousRequests,ContinuousConvMode,ExternalTrigConv,ScanConvMode,NbrOfConversion,Rank-5\#ChannelRegularConversion,Channel-5\#ChannelRegularConversion,SamplingTime-5\#ChannelRegularConversion,EnableAnalogWatchDog
ADC1.NbrOfConversion=2
ADC1.NbrOfConversionFlag=1
ADC1.Rank-3\#ChannelRegularConversion=1
ADC1.Rank-5\#ChannelRegularConversion=2
ADC1.SamplingTime-3\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.SamplingTime-5\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.ScanConvMode=ENABLE
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
DAC.DAC_Channel-DAC_OUT1=DAC_CHANNEL_1
DAC.DAC_Channel-DAC_OUT2=DAC_CHANNEL_2
DAC.IPParameters=DAC_Channel-DAC_OUT1,DAC_Channel-DAC_OUT2
Dma.ADC1.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.0.Instance=DMA2_Stream0
Dma.ADC1.0.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC1.0.MemInc=DMA_MINC_ENABLE
Dma.ADC1.0.Mode=DMA_CIRCULAR
Dma.ADC1.0.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC1.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.0.Priority=DMA_PRIORITY_LOW
Dma.ADC1.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=ADC1
Dma.Request1=USART1_RX
Dma.RequestsNb=2
Dma.USART1_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.1.Instance=DMA2_Stream2
Dma.USART1_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.1.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.1.Mode=DMA_NORMAL
Dma.USART1_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.1.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
FATFS.IPParameters=_CODE_PAGE,_USE_LFN
FATFS._CODE_PAGE=936
FATFS._USE_LFN=3
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Mode=I2C_Fast
I2C1.IPParameters=I2C_Mode
KeepUserPlacement=false
Mcu.CPN=STM32F429ZGT6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=DAC
Mcu.IP10=SYS
Mcu.IP11=TIM14
Mcu.IP12=TIM3
Mcu.IP13=TIM6
Mcu.IP14=USART1
Mcu.IP2=DMA
Mcu.IP3=FATFS
Mcu.IP4=I2C1
Mcu.IP5=NVIC
Mcu.IP6=RCC
Mcu.IP7=RTC
Mcu.IP8=SDIO
Mcu.IP9=SPI2
Mcu.IPNb=15
Mcu.Name=STM32F429ZGTx
Mcu.Package=LQFP144
Mcu.Pin0=PH0/OSC_IN
Mcu.Pin1=PH1/OSC_OUT
Mcu.Pin10=PE9
Mcu.Pin11=PE11
Mcu.Pin12=PE13
Mcu.Pin13=PE15
Mcu.Pin14=PB10
Mcu.Pin15=PB11
Mcu.Pin16=PB12
Mcu.Pin17=PB13
Mcu.Pin18=PB14
Mcu.Pin19=PB15
Mcu.Pin2=PC0
Mcu.Pin20=PD8
Mcu.Pin21=PD9
Mcu.Pin22=PD10
Mcu.Pin23=PD11
Mcu.Pin24=PD12
Mcu.Pin25=PD13
Mcu.Pin26=PD14
Mcu.Pin27=PD15
Mcu.Pin28=PC6
Mcu.Pin29=PC7
Mcu.Pin3=PA4
Mcu.Pin30=PC8
Mcu.Pin31=PC9
Mcu.Pin32=PA9
Mcu.Pin33=PA10
Mcu.Pin34=PA13
Mcu.Pin35=PA14
Mcu.Pin36=PC10
Mcu.Pin37=PC11
Mcu.Pin38=PC12
Mcu.Pin39=PD0
Mcu.Pin4=PA5
Mcu.Pin40=PD1
Mcu.Pin41=PD2
Mcu.Pin42=PD3
Mcu.Pin43=PD4
Mcu.Pin44=PD5
Mcu.Pin45=PD6
Mcu.Pin46=PD7
Mcu.Pin47=PB3
Mcu.Pin48=PB6
Mcu.Pin49=PB7
Mcu.Pin5=PB0
Mcu.Pin50=VP_FATFS_VS_SDIO
Mcu.Pin51=VP_RTC_VS_RTC_Activate
Mcu.Pin52=VP_RTC_VS_RTC_Calendar
Mcu.Pin53=VP_SYS_VS_Systick
Mcu.Pin54=VP_TIM3_VS_ClockSourceINT
Mcu.Pin55=VP_TIM6_VS_ClockSourceINT
Mcu.Pin56=VP_TIM14_VS_ClockSourceINT
Mcu.Pin57=VP_STMicroelectronics.X-CUBE-ALGOBUILD_VS_DSPOoLibraryJjLibrary_1.3.0_1.3.0
Mcu.Pin6=PE7
Mcu.Pin7=PE8
Mcu.Pin8=PE9
Mcu.Pin9=PE11
Mcu.PinsNb=58
Mcu.ThirdParty0=STMicroelectronics.X-CUBE-ALGOBUILD.1.3.0
Mcu.ThirdPartyNb=1
Mcu.UserConstants=
Mcu.UserName=STM32F429ZGTx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.ADC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.RTC_Alarm_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.SDIO_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.TIM1_UP_TIM10_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.TIM8_TRG_COM_TIM14_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA4.Signal=COMP_DAC1_group
PA5.Signal=COMP_DAC2_group
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.GPIOParameters=GPIO_Label
PB0.GPIO_Label=KEY5
PB0.Locked=true
PB0.Signal=GPIO_Input
PB10.Mode=I2C
PB10.Signal=I2C1_SCL
PB11.Mode=I2C
PB11.Signal=I2C1_SDA
PB12.Mode=Full_Duplex_Master
PB12.Signal=SPI2_NSS
PB13.Mode=Full_Duplex_Master
PB13.Signal=SPI2_SCK
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB3.Locked=true
PB3.Signal=SYS_JTDO-SWO
PB6.Locked=true
PB6.Signal=GPIO_Output
PB7.Locked=true
PB7.Signal=GPIO_Output
PC0.Signal=ADCx_IN10
PC10.Mode=SD_4_bits_Wide_bus
PC10.Signal=SDIO_D2
PC11.Mode=SD_4_bits_Wide_bus
PC11.Signal=SDIO_D3
PC12.Mode=SD_4_bits_Wide_bus
PC12.Signal=SDIO_CK
PC6.Locked=true
PC6.Signal=GPIO_Output
PC7.Locked=true
PC7.Signal=GPIO_Output
PC8.Mode=SD_4_bits_Wide_bus
PC8.Signal=SDIO_D0
PC9.Mode=SD_4_bits_Wide_bus
PC9.Signal=SDIO_D1
PD0.Mode=SD_4_bits_Wide_bus
PD0.Signal=SDIO_CMD
PD1.Locked=true
PD1.Signal=GPIO_Output
PD10.Locked=true
PD10.Signal=GPIO_Output
PD11.Locked=true
PD11.Signal=GPIO_Output
PD12.Locked=true
PD12.Signal=GPIO_Output
PD13.Locked=true
PD13.Signal=GPIO_Output
PD14.Locked=true
PD14.Signal=GPIO_Output
PD15.Locked=true
PD15.Signal=GPIO_Output
PD2.Mode=SD_4_bits_Wide_bus
PD2.Signal=SDIO_CMD
PD3.Locked=true
PD3.Signal=GPIO_Output
PD4.Locked=true
PD4.Signal=GPIO_Output
PD5.Locked=true
PD5.Signal=GPIO_Output
PD6.Locked=true
PD6.Signal=GPIO_Output
PD7.Locked=true
PD7.Signal=GPIO_Output
PD8.Locked=true
PD8.Signal=GPIO_Output
PD9.Locked=true
PD9.Signal=GPIO_Output
PE11.GPIOParameters=GPIO_Label
PE11.GPIO_Label=KEY2
PE11.Locked=true
PE11.Signal=GPIO_Input
PE13.GPIOParameters=GPIO_Label
PE13.GPIO_Label=KEY1
PE13.Locked=true
PE13.Signal=GPIO_Input
PE15.GPIOParameters=GPIO_Label
PE15.GPIO_Label=KEY0
PE15.Locked=true
PE15.Signal=GPIO_Input
PE7.GPIOParameters=GPIO_Label
PE7.GPIO_Label=KEY4
PE7.Locked=true
PE7.Signal=GPIO_Input
PE8.Locked=true
PE8.Signal=GPIO_Output
PE9.GPIOParameters=GPIO_Label
PE9.GPIO_Label=KEY3
PE9.Locked=true
PE9.Signal=GPIO_Input
PH0/OSC_IN.Mode=HSE-External-Oscillator
PH0/OSC_IN.Signal=RCC_OSC_IN
PH1/OSC_OUT.Mode=HSE-External-Oscillator
PH1/OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F429ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=GD32_Wangui.ioc
ProjectManager.ProjectName=GD32_Wangui
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_ADC1_Init-ADC1-false-HAL-true,6-MX_DAC_Init-DAC-false-HAL-true,7-MX_TIM3_Init-TIM3-false-HAL-true,8-MX_TIM6_Init-TIM6-false-HAL-true,9-MX_TIM14_Init-TIM14-false-HAL-true,10-MX_I2C1_Init-I2C1-false-HAL-true,11-MX_SPI2_Init-SPI2-false-HAL-true,12-MX_SDIO_SD_Init-SDIO-false-HAL-true,13-MX_FATFS_Init-FATFS-false-HAL-false
RCC.48MHZClocksFreq_Value=48000000
RCC.AHBFreq_Value=120000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=30000000
RCC.APB1TimFreq_Value=60000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=60000000
RCC.APB2TimFreq_Value=120000000
RCC.CortexFreq_Value=120000000
RCC.EthernetFreq_Value=120000000
RCC.FCLKCortexFreq_Value=120000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=120000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=96000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LCDTFTFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQ,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SAI_AClocksFreq_Value,SAI_BClocksFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAIOutputFreq_Value,VCOSAIOutputFreq_ValueQ,VCOSAIOutputFreq_ValueR,VcooutputI2S,VcooutputI2SQ
RCC.LCDTFTFreq_Value=20416666.666666668
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=120000000
