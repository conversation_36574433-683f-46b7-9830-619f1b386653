#include "adc_app.h"
#include "tim.h"

enum { ADC_BUFFER_SIZE = 2048 };
#define BUFFER_SIZE ADC_BUFFER_SIZE

extern DMA_HandleTypeDef hdma_adc1;

static inline uint32_t get_sample_count(void) { return BUFFER_SIZE / 2; }
static inline float adc_to_voltage(uint32_t adc_val) { return (float)adc_val * 3.3f / 4096.0f; }

uint32_t adc_result_buf[BUFFER_SIZE / 2];
__IO uint32_t raw_sample_buffer[BUFFER_SIZE];
__IO float voltage_val;
__IO uint8_t adc_done_flag = 0;

void adc_tim_dma_init(void)
{
    HAL_TIM_Base_Start(&htim3);
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)raw_sample_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
}

void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef *hadc)
{
    UNUSED(hadc);
    if (hadc == &hadc1)
    {
        HAL_ADC_Stop_DMA(hadc);
        adc_done_flag = 1;
    }
}

void adc_task(void)
{
    if (!adc_done_flag) return;

    adc_done_flag = 0;

    uint32_t sum_val = 0;
    const uint16_t sample_count = get_sample_count();

    for (uint16_t i = 0; i < sample_count; i++)
    {
        uint32_t sample = raw_sample_buffer[i * 2];
        adc_result_buf[i] = sample;
        sum_val += sample;
    }

    voltage_val = adc_to_voltage(sum_val / sample_count);

    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)raw_sample_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
}
