#include "system_check.h"
#include "gd25qxx.h"
#include "ff.h"
#include "fatfs.h"
#include "rtc_app.h"
#include "diskio.h"
#include "string.h"

extern RTC_HandleTypeDef hrtc;
extern UART_HandleTypeDef huart1;

typedef struct
{
    uint32_t id;
    const char *model;
    uint32_t capacity_mb;
} flash_model_t;

static const flash_model_t flash_models[] = {
    {0xC84017, "GD25Q64", 8},
    {0xC84016, "GD25Q32", 4},
    {0xC84015, "GD25Q16", 2},
    {0xC84014, "GD25Q80", 1},
    {0xC84013, "GD25Q40", 1},
    {0xEF4017, "W25Q64", 8},
    {0xEF4016, "W25Q32", 4},
    {0xEF4015, "W25Q16", 2},
    {0x000000, "Unknown", 0}
};

const char *get_flash_model_name(uint32_t flash_id)
{
    for (int i = 0; flash_models[i].id != 0x000000; i++)
    {
        if (flash_models[i].id == flash_id)
        {
            return flash_models[i].model;
        }
    }
    return "Unknown";
}

static uint32_t get_flash_capacity(uint32_t flash_id)
{
    for (int i = 0; flash_models[i].id != 0x000000; i++)
    {
        if (flash_models[i].id == flash_id)
        {
            return flash_models[i].capacity_mb;
        }
    }
    return 0;
}

static uint32_t sd_card_capacity_get(void)
{
    DWORD sector_count = 0;
    WORD sector_size = 0;

    if (disk_ioctl(0, GET_SECTOR_COUNT, &sector_count) == RES_OK)
    {
        if (disk_ioctl(0, GET_SECTOR_SIZE, &sector_size) == RES_OK)
        {
            return (uint32_t)((uint64_t)sector_count * sector_size / 1024);
        }
    }
    return 0;
}

system_check_status_t check_flash_status(flash_info_t *flash_info)
{
    if (flash_info == NULL)
    {
        return SYSTEM_CHECK_ERROR;
    }

    flash_info->flash_id = spi_flash_read_id();

    if (flash_info->flash_id == 0x000000 || flash_info->flash_id == 0xFFFFFF)
    {
        flash_info->status = SYSTEM_CHECK_NOT_FOUND;
        strcpy(flash_info->model_name, "Not Found");
        flash_info->capacity_mb = 0;
        return SYSTEM_CHECK_NOT_FOUND;
    }

    const char *model = get_flash_model_name(flash_info->flash_id);
    strcpy(flash_info->model_name, model);
    flash_info->capacity_mb = get_flash_capacity(flash_info->flash_id);
    flash_info->status = SYSTEM_CHECK_OK;

    return SYSTEM_CHECK_OK;
}

system_check_status_t check_tf_card_status(sd_card_info_t *sd_info)
{
    if (sd_info == NULL)
    {
        return SYSTEM_CHECK_ERROR;
    }

    DSTATUS sd_status = disk_initialize(0);
    if (sd_status == 0)
    {
        uint32_t capacity_kb = sd_card_capacity_get();

        sd_info->capacity_mb = capacity_kb / 1024;
        sd_info->sector_size = 512;
        sd_info->sector_count = capacity_kb * 2;
        sd_info->status = SYSTEM_CHECK_OK;

        return SYSTEM_CHECK_OK;
    }
    else
    {
        sd_info->status = SYSTEM_CHECK_NOT_FOUND;
        sd_info->capacity_mb = 0;
        sd_info->sector_count = 0;
        sd_info->sector_size = 0;

        return SYSTEM_CHECK_NOT_FOUND;
    }
}



void print_system_info(const system_info_t *info)
{
    my_printf(&huart1, "=======system selftest=======\r\n");

    if (info->flash_info.status == SYSTEM_CHECK_OK)
    {
        my_printf(&huart1, "flash......ok\r\n");
    }
    else
    {
        my_printf(&huart1, "flash......error\r\n");
    }

    if (info->sd_info.status == SYSTEM_CHECK_OK)
    {
        my_printf(&huart1, "TF card......ok\r\n");
        uint32_t capacity_kb = info->sd_info.capacity_mb * 1024;
        my_printf(&huart1, "TF card memory: %d KB\r\n", capacity_kb);
    }
    else
    {
        my_printf(&huart1, "TF card.......error\r\n");
        my_printf(&huart1, "can not find TF card\r\n");
    }

    my_printf(&huart1, "flash ID:0x%06X\r\n", info->flash_info.flash_id);

    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};

    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);

    my_printf(&huart1, "RTC:%04d-%02d-%02d  %02d:%02d:%02d\r\n",
              current_date.Year + 2000,
              current_date.Month,
              current_date.Date,
              current_time.Hours,
              current_time.Minutes,
              current_time.Seconds);

    my_printf(&huart1, "=======system selftest=======\r\n");
}

void run_device_check(void)
{
    system_info_t system_info;

    memset(&system_info, 0, sizeof(system_info_t));

    system_info.rtc_status = SYSTEM_CHECK_OK;

    if (check_flash_status(&system_info.flash_info) == SYSTEM_CHECK_OK) {
        check_tf_card_status(&system_info.sd_info);
    } else {
        check_tf_card_status(&system_info.sd_info);
    }

    print_system_info(&system_info);
}
